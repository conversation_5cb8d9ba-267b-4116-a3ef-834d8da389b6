import numpy
import base64
import markdown
import io
import sys
import random
from PIL import Image
import os

class Color(numpy.ndarray):
    def __init__(self, r, g, b, a=255):
        super().__init__([r, g, b, a], dtype=numpy.uint8)
    
    def hex(self):
        return '#%02x%02x%02x' % (self[0], self[1], self[2])
    
    def composite(self, other):
        alpha = numpy.uint8((1 - (1 - self[3] / 255) * (1 - other[3] / 255)) * 255)
        tmp = self * self[3] // 255 + other * (255 - self[3]) // 255
        return Color(tmp[0], tmp[1], tmp[2], alpha)

    @staticmethod
    def compute_shadow(foreground, background):
        return Color(
            255 - (255 - foreground[0]) * (255 - background[0]) // 255,
            255 - (255 - foreground[1]) * (255 - background[1]) // 255,
            255 - (255 - foreground[2]) * (255 - background[2]) // 255,
            foreground[3]
        )

def compute_perspective_coeff(target, source):
    '''Compute the perspective coefficients from 4 corresponding points'''
    matrix = []
    for p1, p2 in zip(source, target):
        matrix.append([p1[0], p1[1], 1, 0, 0, 0, -p2[0]*p1[0], -p2[0]*p1[1]])
        matrix.append([0, 0, 0, p1[0], p1[1], 1, -p2[1]*p1[0], -p2[1]*p1[1]])

    A = numpy.matrix(matrix)
    B = numpy.array(target).reshape(8)

    # res = numpy.dot(numpy.linalg.inv(A.T * A) * A.T, B)
    res = np.linalg.solve(A, B)
    return numpy.array(res).reshape(8)

def get_transparent_bounding_box(img):
    width, height = img.size
    pixels = numpy.array(img)
    alpha = pixels[:,:,3] == 0
    rows = numpy.all(alpha, axis=1)
    cols = numpy.all(alpha, axis=0)
    row_lo, row_hi = 0, height-1
    while row_lo <= row_hi and rows[row_lo]: row_lo += 1
    while row_lo <= row_hi and rows[row_hi]: row_hi -= 1
    col_lo, col_hi = 0, width-1
    while col_lo <= col_hi and cols[col_lo]: col_lo += 1
    while col_lo <= col_hi and cols[col_hi]: col_hi -= 1
    return (col_lo, row_lo, col_hi+1, row_hi+1)

def layer_to_image(layer):
    # return layer.composite()
    n = len(layer._channels)
    pixels = numpy.zeros((layer.height, layer.width, n), dtype=numpy.uint8)
    for i in range(n):
        idx = 3 if i==0 else i-1
        pixels[:,:,idx] = layer._channels[i].get_data(layer.height, layer.width, layer._psd.depth)
    return Image.fromarray(pixels)

def set_layer_image(layer, img):
    n = len(layer._channels)
    pixels = numpy.array(img)
    for i in range(n):
        pixel_bytes = pixels[:,:,i].tobytes()
        idx = 0 if i==3 else i+1
        layer._channels[idx].set_data(pixel_bytes, layer.height, layer.width, depth=layer._psd.depth)


def erase_exif(image):
    """Erase exif data from image"""
    image_without_exif = Image.new(image.mode, image.size)
    image_without_exif.putdata(image.getdata())
    return image_without_exif

def base64_to_images(s):
    if type(s) == str:
        s = s.encode('ascii')
    data = base64.decodebytes(s)
    image = Image.open(io.BytesIO(data))
    # open('tmp.jpg', 'wb').write(base64.decodebytes(t))
    # image = Image.open('tmp.jpg')
    return image


def load_markdown_images(filename):
    f = open(filename, 'r')
    # return markdown.markdown( f.read() )
    links = []
    for s in f:
        s = s.strip()
        if s.startswith('!['):
            t = s.find('][')
            if t > 0:
                link = s[t+2:-1]
                links.append(link)
        elif s.startswith('['):
            t = s.find(']:')
            if t > 0:
                link = s[t+2:].strip()
                links.append(link)
    for link in links:
        if link.startswith('data:image/') and link.find(';base64,', 12) > 0:
            ext = link[11:].split(';')[0]
            image = base64_to_images(link.split(';base64,')[1])
            yield image, ext

def save_markdown_images(filename, prefix):
    os.makedirs(prefix, exist_ok=True)
    idx = 0
    for image, ext in load_markdown_images(filename):
        idx += 1
        image.save(f'{prefix}/img_{idx}.{ext}')
        
def stderr_print(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)

def random_string(n, alphabet = None):
    if alphabet is None:
        alphabet = random_string.default_alphabet
    return ''.join(random.choice(alphabet) for _ in range(n))
random_string.default_alphabet = [chr(ord('0')+i) for i in range(10)]+[chr(ord('a')+i) for i in range(26)]

def image_hash(image, size=32):
    img = image.copy()
    img = img.resize((size, size), Image.ANTIALIAS)
    img = img.convert("L")
    pixel_data = list(img.getdata())
    avg_pixel = sum(pixel_data)/len(pixel_data)
    bits = "".join(['1' if (px >= avg_pixel) else '0' for px in pixel_data])
    hex_representation = str(hex(int(bits, 2)))[2:][::-1].upper()
    return hex_representation

def is_same_image(img1, img2):
    return image_hash(img1, 128) == image_hash(img2, 128)

# import imagehash
# def image_hash():
# 	return imagehash.average_hash

def rename_all(path, prefix, ext = None):
    fs = []
    for f in os.listdir(path):
        if ((ext is None) and (is_image_filename(f))) or ((ext is not None) and f.endswith(ext)):
            fs.append(f)
    fs.sort(key=lambda f: os.path.getmtime(os.path.join(path, f)))
    idx = 0
    for f in fs:
        idx += 1
        tmp_ext = f.split('.')[-1]
        if tmp_ext == "None":
            tmp_ext = "png"
        if tmp_ext.startswith('.'):
            tmp_ext = tmp_ext[1:]
        os.rename(os.path.join(path, f), os.path.join(path, prefix + f"image_{idx}.{tmp_ext}"))

def is_image_filename(filename):
    return filename.endswith('.png') or filename.endswith('.jpg') or filename.endswith('.jpeg') or filename.endswith('.webp') or filename.endswith('.jfif')


from PIL import Image, ExifTags
from PIL.ExifTags import TAGS
import json
import re

def parse_prompt_with_weights(prompt_string):
    """
    Parse enhanced prompt string into list of (tag, weight) tuples.

    Supports various weight formats:
    - {tag} = (tag, 1.1)
    - [tag] = (tag, 0.9)
    - (tag:1.2) = (tag, 1.2)
    - tag = (tag, 1.0)
    - --lora filename:weight = (filename, weight)
    - --cfg 7.5 = ('cfg', 7.5)

    Args:
        prompt_string: Enhanced prompt string

    Returns:
        list: List of (tag, weight) tuples
    """
    if not isinstance(prompt_string, str):
        return []

    tags_with_weights = []

    # Split by commas and process each part
    parts = [part.strip() for part in prompt_string.split(',')]

    for part in parts:
        if not part:
            continue

        # Handle LoRA tags: --lora filename:weight
        if part.startswith('--lora '):
            lora_part = part[7:].strip()  # Remove '--lora '
            if ':' in lora_part:
                filename, weight_str = lora_part.rsplit(':', 1)
                try:
                    weight = float(weight_str)
                    tags_with_weights.append((filename.strip(), weight))
                except ValueError:
                    tags_with_weights.append((lora_part, 1.0))
            else:
                tags_with_weights.append((lora_part, 1.0))
            continue

        # Handle other command-line style parameters
        if part.startswith('--'):
            cmd_parts = part.split()
            if len(cmd_parts) >= 2:
                param_name = cmd_parts[0][2:]  # Remove '--'
                try:
                    param_value = float(cmd_parts[1])
                    tags_with_weights.append((param_name, param_value))
                except ValueError:
                    tags_with_weights.append((param_name, cmd_parts[1]))
            continue

        # Handle weighted tags: {tag}, [tag], (tag:weight)
        # Pattern for (tag:weight)
        weight_match = re.match(r'\(([^:]+):([0-9.]+)\)', part)
        if weight_match:
            tag = weight_match.group(1).strip()
            try:
                weight = float(weight_match.group(2))
                tags_with_weights.append((tag, weight))
            except ValueError:
                tags_with_weights.append((tag, 1.0))
            continue

        # Pattern for {tag} (emphasis)
        if part.startswith('{') and part.endswith('}'):
            tag = part[1:-1].strip()
            tags_with_weights.append((tag, 1.1))
            continue

        # Pattern for [tag] (de-emphasis)
        if part.startswith('[') and part.endswith(']'):
            tag = part[1:-1].strip()
            tags_with_weights.append((tag, 0.9))
            continue

        # Regular tag without special formatting
        tags_with_weights.append((part.strip(), 1.0))

    return tags_with_weights


def convert_api_workflow_to_standard(workflow):
    """
    Convert ComfyUI API format workflow to standard format for processing.

    Args:
        workflow: Workflow dictionary (either API format or standard format)

    Returns:
        dict: Workflow in standard format
    """
    if not isinstance(workflow, dict):
        return workflow

    # Check if this is API format (has 'nodes' key)
    if 'nodes' in workflow and isinstance(workflow['nodes'], list):
        converted_workflow = {}
        for node in workflow['nodes']:
            if isinstance(node, dict) and 'id' in node:
                node_id = str(node['id'])

                # Convert inputs from API format to standard format
                api_inputs = node.get('inputs', {})
                standard_inputs = {}

                if isinstance(api_inputs, dict):
                    for input_name, input_value in api_inputs.items():
                        # In API format, connections are stored as {"name": "input_name", "type": "...", "link": link_id}
                        # We need to convert these to [node_id, output_index] format
                        if isinstance(input_value, dict) and 'link' in input_value:
                            # This is a connection - we need to find the source node
                            # For now, we'll store the link info and resolve later
                            standard_inputs[input_name] = input_value
                        else:
                            # This is a direct value
                            standard_inputs[input_name] = input_value
                elif isinstance(api_inputs, list):
                    # API format stores inputs as list of {"name": "input_name", "type": "...", "link": link_id}
                    for input_def in api_inputs:
                        if isinstance(input_def, dict) and 'name' in input_def:
                            input_name = input_def['name']
                            if 'link' in input_def and input_def['link'] is not None:
                                # This is a connection - store link info for later resolution
                                standard_inputs[input_name] = input_def
                            else:
                                # This is a direct value or unconnected input
                                # Check if there's a 'value' field or use default
                                standard_inputs[input_name] = input_def.get('value', '')

                # Also check for widget_values which contain the actual LoRA names and weights
                widget_values = node.get('widgets_values', [])
                if widget_values and node.get('type') == 'LoraLoader':
                    # For LoraLoader, widget_values typically contains [lora_name, strength_model, strength_clip]
                    if len(widget_values) >= 3:
                        lora_name = widget_values[0]
                        strength_model = widget_values[1]
                        strength_clip = widget_values[2]
                        # Create a text field similar to LoraTagLoader format
                        # Round the strength to avoid floating point precision issues
                        try:
                            strength_rounded = round(float(strength_model), 2)
                            standard_inputs['text'] = f"<lora:{lora_name}:{strength_rounded}>"
                        except (ValueError, TypeError):
                            standard_inputs['text'] = f"<lora:{lora_name}:{strength_model}>"

                converted_workflow[node_id] = {
                    'class_type': node.get('type', 'Unknown'),
                    'inputs': standard_inputs,
                    '_meta': {'title': node.get('title', 'No title')}
                }

        # Second pass: resolve connections using links
        if 'links' in workflow and isinstance(workflow['links'], list):
            # Create a mapping from link_id to [source_node_id, output_index]
            link_map = {}
            for link in workflow['links']:
                if isinstance(link, list) and len(link) >= 5:
                    link_id, source_node_id, output_index, target_node_id, input_index = link[:5]
                    link_map[link_id] = [str(source_node_id), output_index]

            # Update inputs to use proper node references
            for node_id, node in converted_workflow.items():
                inputs = node['inputs']
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, dict) and 'link' in input_value:
                        link_id = input_value['link']
                        if link_id is not None and link_id in link_map:
                            inputs[input_name] = link_map[link_id]
                        else:
                            # Unconnected input, remove it or set to default
                            inputs[input_name] = input_value.get('value', '')

        return converted_workflow

    # Already in standard format
    return workflow


def extract_connected_lora_nodes(workflow, connected_nodes):
    """
    Extract LoRA information from nodes that are actually connected in the execution path.

    Args:
        workflow: Parsed ComfyUI workflow dictionary
        connected_nodes: Set of node IDs that are connected to the execution path

    Returns:
        list: List of LoRA strings in format "filename:weight"

    Raises:
        TypeError: If workflow is not a dictionary or connected_nodes is not a set
        ValueError: If workflow contains invalid node structures
    """
    if not isinstance(workflow, dict):
        raise TypeError(f"Workflow must be a dictionary, got {type(workflow)}")

    if not isinstance(connected_nodes, set):
        raise TypeError(f"Connected nodes must be a set, got {type(connected_nodes)}")

    loras = []
    lora_node_count = 0

    for node_id, node in workflow.items():
        # Validate node structure
        if not isinstance(node, dict):
            continue  # Skip invalid nodes

        # Only process LoRA nodes that are in the connected execution path
        if (node_id in connected_nodes and
            node.get('class_type') in ['LoraTagLoader', 'LoraLoader']):

            lora_node_count += 1
            inputs = node.get('inputs', {})

            if not isinstance(inputs, dict):
                continue  # Skip nodes with invalid inputs

            lora_text = inputs.get('text', '')

            # Handle case where text might be a list or other non-string type
            if isinstance(lora_text, list):
                # If it's a list, try to join it or take the first element
                if lora_text and isinstance(lora_text[0], str):
                    lora_text = lora_text[0]
                else:
                    continue  # Skip if list is empty or contains non-strings
            elif not isinstance(lora_text, str):
                continue  # Skip nodes with invalid text input

            if lora_text:
                try:
                    # Parse LoRA strings like "<lora:filename.safetensors:1.0>"
                    lora_matches = re.findall(r'<lora:([^>]+)>', lora_text)
                    for match in lora_matches:
                        # Extract filename and weight
                        if ':' in match:
                            parts = match.split(':')
                            if len(parts) >= 2:
                                filename = parts[0].replace('\\', '/').split('/')[-1]  # Get just filename
                                weight = parts[1] if len(parts) > 1 else '1.0'

                                # Validate weight is a number and round to avoid precision issues
                                try:
                                    weight_float = float(weight)
                                    weight_rounded = round(weight_float, 2)
                                    loras.append(f"{filename}:{weight_rounded}")
                                except ValueError:
                                    # Invalid weight, use default
                                    loras.append(f"{filename}:1.0")
                            else:
                                loras.append(match)
                        else:
                            loras.append(match)
                except re.error as e:
                    # Regex error, skip this node
                    continue

    return loras


def traverse_workflow_graph(workflow):
    """
    Traverse ComfyUI workflow graph backward from output nodes to find connected execution path.

    Args:
        workflow: Parsed ComfyUI workflow dictionary

    Returns:
        set: Set of node IDs that are connected to the execution path

    Raises:
        ValueError: If workflow is invalid or no output nodes found
        TypeError: If workflow is not a dictionary
    """
    if not isinstance(workflow, dict):
        raise TypeError(f"Workflow must be a dictionary, got {type(workflow)}")

    if not workflow:
        raise ValueError("Workflow dictionary is empty")

    connected_nodes = set()
    nodes_to_visit = []

    # Find all SaveImage nodes as starting points
    for node_id, node in workflow.items():
        if not isinstance(node, dict):
            continue  # Skip invalid nodes
        if node.get('class_type') == 'SaveImage':
            nodes_to_visit.append(node_id)
            connected_nodes.add(node_id)

    # If no SaveImage nodes found, look for other output node types
    if not nodes_to_visit:
        output_node_types = [
            'PreviewImage', 'SaveImageWebsocket', 'DisplayAny',
            'UltimateSDUpscale', 'FaceDetailer', 'ImageScale',
            'HighRes-Fix Script', 'easy imageColorMatch'
        ]
        for node_id, node in workflow.items():
            if not isinstance(node, dict):
                continue  # Skip invalid nodes
            if node.get('class_type') in output_node_types:
                nodes_to_visit.append(node_id)
                connected_nodes.add(node_id)

    # If still no output nodes found, raise an error
    if not nodes_to_visit:
        available_types = [node.get('class_type', 'Unknown') for node in workflow.values() if isinstance(node, dict)]
        raise ValueError(f"No output nodes found in workflow. Available node types: {set(available_types)}")

    # Backward traversal through the graph
    while nodes_to_visit:
        current_node_id = nodes_to_visit.pop(0)
        current_node = workflow.get(current_node_id, {})

        if not isinstance(current_node, dict):
            continue  # Skip invalid nodes

        inputs = current_node.get('inputs', {})

        if not isinstance(inputs, dict):
            continue  # Skip nodes with invalid inputs

        # Process all input connections
        for input_name, input_value in inputs.items():
            # Check if input_value is a node reference [node_id, output_index]
            if (isinstance(input_value, list) and
                len(input_value) == 2 and
                isinstance(input_value[0], str)):

                referenced_node_id = input_value[0]

                # Validate that the referenced node exists
                if referenced_node_id not in workflow:
                    continue  # Skip references to non-existent nodes

                # Add to connected nodes and queue for traversal if not already processed
                if referenced_node_id not in connected_nodes:
                    connected_nodes.add(referenced_node_id)
                    nodes_to_visit.append(referenced_node_id)

    return connected_nodes


def extract_comfyui_workflow_params(workflow_json_str):
    """
    Extract comprehensive parameters from ComfyUI workflow JSON using graph traversal
    to find only connected nodes in the execution path.

    Args:
        workflow_json_str: JSON string containing ComfyUI workflow

    Returns:
        dict: Extracted parameters including cfg, steps, dimensions, loras, etc.

    Raises:
        json.JSONDecodeError: If workflow JSON is invalid
        ValueError: If workflow structure is invalid
        TypeError: If input is not a string
    """
    if not isinstance(workflow_json_str, str):
        raise TypeError(f"Workflow JSON must be a string, got {type(workflow_json_str)}")

    if not workflow_json_str.strip():
        raise ValueError("Workflow JSON string is empty")

    try:
        workflow = json.loads(workflow_json_str)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Failed to parse workflow JSON: {e.msg}", e.doc, e.pos)

    # Convert API format to standard format if needed
    workflow = convert_api_workflow_to_standard(workflow)

    params = {}

    try:
        # First, traverse the workflow graph to find connected nodes
        connected_nodes = traverse_workflow_graph(workflow)
    except (ValueError, TypeError) as e:
        raise ValueError(f"Failed to traverse workflow graph: {e}")

    # Extract sampling parameters (KSampler node)
    for node_id, node in workflow.items():
        if node.get('class_type') == 'KSampler':
            inputs = node.get('inputs', {})
            params['cfg'] = inputs.get('cfg')
            params['steps'] = inputs.get('steps')
            params['seed'] = inputs.get('seed')
            params['sampler_name'] = inputs.get('sampler_name')
            params['scheduler'] = inputs.get('scheduler')
            params['denoise'] = inputs.get('denoise')
            break

    # Extract image dimensions (EmptyLatentImage node)
    for node_id, node in workflow.items():
        if node.get('class_type') == 'EmptyLatentImage':
            inputs = node.get('inputs', {})
            params['width'] = inputs.get('width')
            params['height'] = inputs.get('height')
            params['batch_size'] = inputs.get('batch_size')
            break

    # Extract model name (CheckpointLoaderSimple node)
    for node_id, node in workflow.items():
        if node.get('class_type') == 'CheckpointLoaderSimple':
            inputs = node.get('inputs', {})
            params['model'] = inputs.get('ckpt_name')
            break

    try:
        # Extract LoRA information using graph traversal (only connected nodes)
        loras = extract_connected_lora_nodes(workflow, connected_nodes)
        if loras:
            params['loras'] = loras
    except (ValueError, TypeError) as e:
        # If LoRA extraction fails, continue without LoRAs but log the error
        pass

    # Extract positive prompt (CLIPTextEncode nodes)
    for node_id, node in workflow.items():
        if (node.get('class_type') == 'CLIPTextEncode' and
            node.get('_meta', {}).get('title') == 'CLIP Text Encode (Prompt)'):
            inputs = node.get('inputs', {})
            prompt_text = inputs.get('text', '')
            if isinstance(prompt_text, str) and prompt_text and 'embedding:' not in prompt_text.lower():  # Skip negative prompts
                params['positive_prompt'] = prompt_text.strip()
                break

    # Extract negative prompt
    for node_id, node in workflow.items():
        if node.get('class_type') == 'CLIPTextEncode':
            title = node.get('_meta', {}).get('title', '')
            inputs = node.get('inputs', {})
            text_input = inputs.get('text', '')

            # Check if this is a negative prompt node
            is_negative = False
            if isinstance(title, str) and 'negative' in title.lower():
                is_negative = True
            elif isinstance(text_input, str) and 'embedding:' in text_input.lower():
                is_negative = True

            if is_negative and isinstance(text_input, str) and text_input:
                params['negative_prompt'] = text_input.strip()
                break

    return params

def format_enhanced_prompt(base_prompt, params):
    """
    Format extracted parameters into a unified command-line style prompt.

    Args:
        base_prompt: Original text prompt
        params: Dictionary of extracted parameters

    Returns:
        str: Enhanced prompt with parameters in --arg format
    """
    if not base_prompt:
        base_prompt = ""

    # Start with base prompt
    enhanced_parts = [base_prompt.strip()]

    # Add parameters in command-line argument style
    if params.get('cfg') is not None:
        enhanced_parts.append(f"--cfg {params['cfg']}")

    if params.get('steps') is not None:
        enhanced_parts.append(f"--steps {params['steps']}")

    if params.get('width') is not None and params.get('height') is not None:
        enhanced_parts.append(f"--size {params['width']}x{params['height']}")

    if params.get('sampler_name'):
        enhanced_parts.append(f"--sampler {params['sampler_name']}")

    if params.get('scheduler'):
        enhanced_parts.append(f"--scheduler {params['scheduler']}")

    if params.get('model'):
        # Extract just the model name without .safetensors extension
        model_name = params['model'].replace('.safetensors', '').replace('.ckpt', '')
        enhanced_parts.append(f"--model {model_name}")

    if params.get('loras'):
        for lora in params['loras']:
            enhanced_parts.append(f"--lora {lora}")

    if params.get('seed') is not None:
        enhanced_parts.append(f"--seed {params['seed']}")

    if params.get('denoise') is not None and params['denoise'] != 1.0:
        enhanced_parts.append(f"--denoise {params['denoise']}")

    return ' '.join(enhanced_parts)

def image_info(image, filename = None, enhanced_prompts = True):
    def strlocate(s, t):
        idx = s.find(t)
        if idx >= 0:
            return s[idx+len(t):]
        return ""
    #image = Image.open(filename).convert("RGB")
    strimg = []
    w, h = image.size
    info_dict = {
        "Filename": filename,
        "Image Height": h,
        "Image Width": w,
        "Image is Animated": getattr(image, "is_animated", False),
        "Frames in Image": getattr(image, "n_frames", 1)
    }
    for label,value in info_dict.items():
        strimg.append(f"{label}: {value}")

    exifdata = image.getexif()
    for tag_id in exifdata:
        tag = TAGS.get(tag_id, tag_id)
        data = exifdata.get(tag_id)
        if isinstance(data, bytes):
            data = data.decode()
        strimg.append(f"{tag}: {data}")

    for x in image.info:
        if x == "exif": continue
        y = image.info[x]
        if type(y) is str:
            y = ''.join(c for c in y if c.isprintable() or c.isspace())
        strimg.append(f"{x}: {y}")
    if "exif" in image.info:
        x = image.info["exif"]
        x = x.replace(b'\x00', b'')
        y = x.decode('utf-8', errors='ignore')
        y = ''.join(c for c in y if c.isprintable() or c.isspace())
        strimg.append(y)
    strimg = '\n'.join(strimg)

    # Extract basic prompt using existing logic
    prompt = strlocate(strimg, 'parameters: ')
    if prompt == "":
        prompt = strlocate(strimg, "ExifII")
        prompt = strlocate(prompt, "UNICODE")
    if prompt.find("\nNegative prompt") >= 0:
        prompt = prompt[:prompt.find("\nNegative prompt")]
    if prompt == "":
        prompt = strlocate(strimg, '{"inputs": {"text": "')
        if prompt.find('"') >= 0:
            prompt = prompt[:prompt.find('"')]
    if prompt == "":
        prompt = strlocate(strimg, 'Description: ')
        if prompt.find("Software: ") >= 0:
            prompt = prompt[:prompt.find("Software: ")]

    # Clean up prompt - remove workflow JSON if it got included
    if prompt.find(' workflow:') >= 0:
        prompt = prompt[:prompt.find(' workflow:')]
    if prompt.find('\nworkflow:') >= 0:
        prompt = prompt[:prompt.find('\nworkflow:')]

    prompt = prompt.replace('\n', ' ').strip()

    # Enhanced prompt extraction with ComfyUI workflow parameters
    enhanced_prompt = prompt
    workflow_params = {}

    if enhanced_prompts:
        # Look for ComfyUI workflow JSON in the metadata
        workflow_json = ""

        # Method 1: Check if workflow is directly available in image.info (most common for ComfyUI)
        if hasattr(image, 'info') and 'workflow' in image.info:
            workflow_data = image.info['workflow']
            if isinstance(workflow_data, str):
                # Workflow is stored as a JSON string
                workflow_json = workflow_data
            elif isinstance(workflow_data, dict):
                # Workflow is already parsed as a dictionary
                workflow_json = json.dumps(workflow_data)

        # Method 2: Check for workflow in various metadata fields (legacy approach)
        if not workflow_json and 'workflow' in strimg:
            workflow_start = strimg.find('workflow:')
            if workflow_start >= 0:
                # Find the JSON object for workflow
                brace_count = 0
                start_pos = strimg.find('{', workflow_start)
                if start_pos >= 0:
                    for i, char in enumerate(strimg[start_pos:], start_pos):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                workflow_json = strimg[start_pos:i+1]
                                break

        # Method 3: Look for direct JSON workflow pattern in metadata string
        if not workflow_json:
            # Look for patterns like {"4": {"inputs": ...}} which indicate ComfyUI workflow
            json_pattern = re.search(r'\{"?\d+"?\s*:\s*\{[^}]*"class_type"[^}]*\}.*?\}', strimg, re.DOTALL)
            if json_pattern:
                # Try to extract the full JSON by finding balanced braces
                start_pos = json_pattern.start()
                brace_count = 0
                for i, char in enumerate(strimg[start_pos:], start_pos):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            workflow_json = strimg[start_pos:i+1]
                            break

        # Extract parameters from workflow JSON
        if workflow_json:
            workflow_params = extract_comfyui_workflow_params(workflow_json)

            # If we found workflow parameters, create enhanced prompt
            if workflow_params:
                # Use positive_prompt from workflow if available, otherwise use extracted prompt
                base_prompt = workflow_params.get('positive_prompt', prompt)
                enhanced_prompt = format_enhanced_prompt(base_prompt, workflow_params)

        # Fallback: try to extract some parameters from the raw string even without full JSON
        if not workflow_params:
            # Look for individual parameters in the metadata string
            fallback_params = {}

            # Extract CFG scale
            cfg_match = re.search(r'"cfg":\s*([0-9.]+)', strimg)
            if cfg_match:
                fallback_params['cfg'] = float(cfg_match.group(1))

            # Extract steps
            steps_match = re.search(r'"steps":\s*(\d+)', strimg)
            if steps_match:
                fallback_params['steps'] = int(steps_match.group(1))

            # Extract dimensions
            width_match = re.search(r'"width":\s*(\d+)', strimg)
            height_match = re.search(r'"height":\s*(\d+)', strimg)
            if width_match and height_match:
                fallback_params['width'] = int(width_match.group(1))
                fallback_params['height'] = int(height_match.group(1))

            # Extract sampler
            sampler_match = re.search(r'"sampler_name":\s*"([^"]+)"', strimg)
            if sampler_match:
                fallback_params['sampler_name'] = sampler_match.group(1)

            # Extract LoRAs from text
            lora_matches = re.findall(r'<lora:([^>]+)>', strimg)
            if lora_matches:
                loras = []
                for match in lora_matches:
                    if ':' in match:
                        parts = match.split(':')
                        filename = parts[0].replace('\\', '/').split('/')[-1]
                        weight = parts[1] if len(parts) > 1 else '1.0'
                        loras.append(f"{filename}:{weight}")
                    else:
                        loras.append(match)
                fallback_params['loras'] = loras

            # If we found any fallback parameters, enhance the prompt
            if fallback_params:
                enhanced_prompt = format_enhanced_prompt(prompt, fallback_params)
                workflow_params = fallback_params

    model_hash = ''
    for k in ['Model hash: ', '"hash":']:
        if strimg.find(k)>=0 and model_hash == '':
            model_hash = strimg[strimg.find(k)+len(k)-1:].split()[0].split(',')[0]

    model_name = ''
    #{"ckpt_name": "animagineXLV3_v30.safetensors"}
    if strimg.find('ckpt_name') >= 0:
        model_name = strimg[strimg.find('ckpt_name')+len('ckpt_name'):].split('}')[0].replace('"', '').replace(':', '').replace(' ', '').split('.')[0]
    elif strimg.find('Model hash:') >= 0:
        tmp =  strimg[strimg.find('Model hash:')+len('Model hash:'):]
        if tmp.find('Model:') >= 0:
            model_name = tmp[tmp.find('Model:')+len('Model:'):].strip().split()[0].replace(',', '').strip()

    ret = {}
    ret["raw"] = strimg
    ret["prompt"] = enhanced_prompt if enhanced_prompts else prompt
    ret["original_prompt"] = prompt  # Keep original for backward compatibility

    # Add extracted workflow parameters
    if workflow_params:
        ret["workflow_params"] = workflow_params

        # Add individual parameters for easy access
        if 'cfg' in workflow_params:
            ret["cfg"] = workflow_params['cfg']
        if 'steps' in workflow_params:
            ret["steps"] = workflow_params['steps']
        if 'width' in workflow_params and 'height' in workflow_params:
            ret["dimensions"] = f"{workflow_params['width']}x{workflow_params['height']}"
        if 'loras' in workflow_params:
            ret["loras"] = workflow_params['loras']
        if 'sampler_name' in workflow_params:
            ret["sampler"] = workflow_params['sampler_name']

    if model_hash != '':
        ret["model_hash"] = model_hash
    if model_name != '':
        ret["model_name"] = model_name

    # Add model from workflow if available and not already set
    if workflow_params.get('model') and not model_name:
        ret["model_name"] = workflow_params['model'].replace('.safetensors', '').replace('.ckpt', '')

    return ret

def foreach_image(dir, recursive = True):
    qs = [dir]
    i = 0
    while i < len(qs):
        d = qs[i]
        i += 1
        #if d == '.' or d == '..': continue
        if os.path.isdir(d):
            if not recursive and i != 1:
                continue
            for f in sorted(os.listdir(d), key=lambda f: int(''.join(filter(str.isdigit, '0'+f)))):
                x = f"{d}/{f}"
                qs.append(x)
        elif is_image_filename(d):
            yield d

def update_dir(dir):
    outimgs = os.path.join(dir, 'imgs')
    outtmps = os.path.join(dir, 'tmps')
    img_hash = {}
    def add_hash(h, f):
        if h not in img_hash:
            img_hash[h] = []
        img_hash[h].append(f)
    if os.path.exists(outimgs):
        for f in filter(is_image_filename, os.listdir(outimgs)):
            ff = os.path.join(outimgs, f)
            img = Image.open(ff)
            add_hash(image_hash(img), ff)
    os.makedirs(outimgs, exist_ok=True)
    os.makedirs(outtmps, exist_ok=True)
    fs = filter(is_image_filename, os.listdir(dir))
    for f in fs:
        ff = os.path.join(dir, f)
        try:
            img = Image.open(ff)
        except:
            continue

        info = image_info(img, ff)
        if "prompt" not in info or len(info["prompt"]) == 0:
            img.close()
            os.rename(ff, os.path.join(outtmps, f))
            continue

        h = image_hash(img)
        exists = False
        if h in img_hash:
            for f2 in img_hash[h]:
                img2 = Image.open(f2)
                if is_same_image(img, img2):
                    exists = True
                    break
        img.close()
        if not exists:
            g = random_string(20)
            ext = os.path.splitext(f)[1]
            while os.path.exists(os.path.join(outimgs, f'{g}{ext}')):
                g = random_string(20)
            outf = os.path.join(outimgs, f'{g}{ext}')
            os.rename(ff, outf)
            add_hash(h, outf)
        else:
            os.remove(ff)


import numpy as np
from PIL import Image, ImageOps, ImageSequence
def comfy_load_image(filename):
    import torch
    img = Image.open(filename)
    output_images = []
    output_masks = []
    for i in ImageSequence.Iterator(img):
        i = ImageOps.exif_transpose(i)
        if i.mode == 'I':
            i = i.point(lambda i: i * (1 / 255))
        image = i.convert("RGB")
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image)[None,]
        if 'A' in i.getbands():
            mask = np.array(i.getchannel('A')).astype(np.float32) / 255.0
            mask = 1. - torch.from_numpy(mask)
        else:
            mask = torch.zeros((64,64), dtype=torch.float32, device="cpu")
        output_images.append(image)
        output_masks.append(mask.unsqueeze(0))

    if len(output_images) > 1:
        output_image = torch.cat(output_images, dim=0)
        output_mask = torch.cat(output_masks, dim=0)
    else:
        output_image = output_images[0]
        output_mask = output_masks[0]

    return (output_image, output_mask)

def comfy_load_torch_file(ckpt, device=None):
    import torch
    if device is None:
        device = torch.device("cpu")
    if ckpt.lower().endswith(".safetensors"):
        import safetensors.torch
        sd = safetensors.torch.load_file(ckpt, device=device.type)
    else:
        pl_sd = torch.load(ckpt, map_location=device, weights_only=True)
        if "state_dict" in pl_sd:
            sd = pl_sd["state_dict"]
        else:
            sd = pl_sd
    return sd

def get_vae():
    global vae
    if "vae" not in globals():
        import torch
        from sdvae import VAE
        import pickle
        script_directory = os.path.abspath(sys.argv[0])
        if not os.path.isdir(script_directory):
            script_directory = os.path.dirname(script_directory)
        sd = pickle.load(open(script_directory + '/res/' + "extracted_vae_weights.pkl", "rb"))
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        vae = VAE(sd, device)
    return vae


def vae_encode_crop_pixels(pixels):
    x = (pixels.shape[1] // 8) * 8
    y = (pixels.shape[2] // 8) * 8
    if pixels.shape[1] != x or pixels.shape[2] != y:
        x_offset = (pixels.shape[1] % 8) // 2
        y_offset = (pixels.shape[2] % 8) // 2
        pixels = pixels[:, x_offset:x + x_offset, y_offset:y + y_offset, :]
    return pixels

def vae_encode(pixels):
    vae = get_vae()
    import torch
    if type(pixels) != torch.Tensor:
        if type(pixels) != np.ndarray:
            pixels = np.array(pixels)
        pixels = torch.from_numpy(pixels)[None,]
    return vae.encode(vae_encode_crop_pixels(pixels)[:,:,:,:3])

def vae_decode(samples):
    vae = get_vae()
    return vae.decode(samples)


import numpy as np


from mixbox import _lut
import numpy as np

_POLY_COEFFS = np.array([
    [+0.07717053, +0.02826978, +0.24832992],
    [+0.95912302, +0.80256528, +0.03561839],
    [+0.74683774, +0.04868586, +0.00000000],
    [+0.99518138, +0.99978149, +0.99704802],
    [+0.04819146, +0.83363781, +0.32515377],
    [-0.68146950, +1.46107803, +1.06980936],
    [+0.27058419, -0.15324870, +1.98735057],
    [+0.80478189, +0.67093710, +0.18424500],
    [-0.35031003, +1.37855826, +3.68865000],
    [+1.05128046, +1.97815239, +2.82989073],
    [+3.21607125, +0.81270228, +1.03384539],
    [+2.78893374, +0.41565549, -0.04487295],
    [+3.02162577, +2.55374103, +0.32766114],
    [+2.95124691, +2.81201112, +1.17578442],
    [+2.82677043, +0.79933038, +1.81715262],
    [+2.99691099, +1.22593053, +1.80653661],
    [+1.87394106, +2.05027182, -0.29835996],
    [+2.56609566, +7.03428198, +0.62575374],
    [+4.08329484, -1.40408358, +2.14995522],
    [+6.00078678, +2.55552042, +1.90739502]
])

def rgb_to_latent_np(rgb):
    rgb = np.asarray(rgb).astype(np.float32) / 255.0
    has_alpha = rgb.shape[-1] == 4

    r = np.clip(rgb[..., 0], 0.0, 1.0)
    g = np.clip(rgb[..., 1], 0.0, 1.0)
    b = np.clip(rgb[..., 2], 0.0, 1.0)

    x = r * 63.0
    y = g * 63.0
    z = b * 63.0

    ix = np.floor(x).astype(np.int32)
    iy = np.floor(y).astype(np.int32)
    iz = np.floor(z).astype(np.int32)

    tx = x - ix
    ty = y - iy
    tz = z - iz

    xyz = (ix + iy * 64 + iz * 64 * 64) & 0x3FFFF

    c0 = np.zeros_like(r)
    c1 = np.zeros_like(r)
    c2 = np.zeros_like(r)

    for dx in [0, 1]:
        wx = (1 - tx) if dx == 0 else tx
        for dy in [0, 1]:
            wy = (1 - ty) if dy == 0 else ty
            for dz in [0, 1]:
                wz = (1 - tz) if dz == 0 else tz
                w = wx * wy * wz
                offset = xyz + dx + dy * 64 + dz * 64 * 64
                c0 += w * _lut[offset + 192]
                c1 += w * _lut[offset + 262336]
                c2 += w * _lut[offset + 524480]

    c0 /= 255.0
    c1 /= 255.0
    c2 /= 255.0
    c3 = 1.0 - (c0 + c1 + c2)

    mixrgb = eval_polynomial_np(c0, c1, c2, c3)
    residual_r = r - mixrgb[..., 0]
    residual_g = g - mixrgb[..., 1]
    residual_b = b - mixrgb[..., 2]

    latent = np.stack([c0, c1, c2, c3, residual_r, residual_g, residual_b], axis=-1)

    if has_alpha:
        alpha = rgb[..., 3:4]
        latent = np.concatenate((latent, alpha), axis=-1)

    return latent


def latent_to_rgb_np(latent):
    has_alpha = latent.shape[-1] == 8

    c0, c1, c2, c3 = latent[..., 0], latent[..., 1], latent[..., 2], latent[..., 3]
    residual_r, residual_g, residual_b = latent[..., 4], latent[..., 5], latent[..., 6]

    rgb_base = eval_polynomial_np(c0, c1, c2, c3)
    r = np.clip(rgb_base[..., 0] + residual_r, 0.0, 1.0)
    g = np.clip(rgb_base[..., 1] + residual_g, 0.0, 1.0)
    b = np.clip(rgb_base[..., 2] + residual_b, 0.0, 1.0)

    rgb = np.stack([r, g, b], axis=-1)
    rgb = (rgb * 255.0).round().astype(np.uint8)

    if has_alpha:
        alpha = np.clip(latent[..., 7], 0.0, 1.0)
        alpha = (alpha * 255.0).round().astype(np.uint8)
        rgb = np.concatenate((rgb, alpha[..., None]), axis=-1)

    return rgb


def eval_polynomial_np(c0, c1, c2, c3):
    terms = np.stack([
        c0**3,
        c1**3,
        c2**3,
        c3**3,
        c0**2 * c1,
        c0 * c1**2,
        c0**2 * c2,
        c0 * c2**2,
        c0**2 * c3,
        c0 * c3**2,
        c1**2 * c2,
        c1 * c2**2,
        c1**2 * c3,
        c1 * c3**2,
        c2**2 * c3,
        c2 * c3**2,
        c0 * c1 * c2,
        c0 * c1 * c3,
        c0 * c2 * c3,
        c1 * c2 * c3
    ], axis=-1)

    rgb = np.tensordot(terms, _POLY_COEFFS, axes=([-1], [0]))
    return rgb

